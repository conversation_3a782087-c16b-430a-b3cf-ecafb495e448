#!/usr/bin/env node

/**
 * 自动版本号递增构建脚本
 * 使用方法: node build-with-version-bump.js
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// 定义文件路径
const CARGO_TOML = 'src-tauri/Cargo.toml';
const TAURI_CONF = 'src-tauri/tauri.conf.json';
const INSTALLER_NSI = 'src-tauri/nsis/installer.nsi';

/**
 * 递增版本号
 * @param {string} version - 当前版本号 (例如: "1.0.4")
 * @returns {string} 新版本号 (例如: "1.0.5")
 */
function incrementVersion(version) {
    const parts = version.split('.');
    const major = parseInt(parts[0]);
    const minor = parseInt(parts[1]);
    const patch = parseInt(parts[2]) + 1;
    
    return `${major}.${minor}.${patch}`;
}

/**
 * 从 Cargo.toml 获取当前版本号
 * @returns {string} 当前版本号
 */
function getCurrentVersion() {
    const content = fs.readFileSync(CARGO_TOML, 'utf8');
    const match = content.match(/^version = "(.+)"$/m);
    
    if (!match) {
        throw new Error('无法从 Cargo.toml 中找到版本号');
    }
    
    return match[1];
}

/**
 * 更新 Cargo.toml 中的版本号
 * @param {string} newVersion - 新版本号
 */
function updateCargoToml(newVersion) {
    console.log(`📝 更新 ${CARGO_TOML} 版本号到 ${newVersion}`);
    
    let content = fs.readFileSync(CARGO_TOML, 'utf8');
    content = content.replace(/^version = ".*"$/m, `version = "${newVersion}"`);
    fs.writeFileSync(CARGO_TOML, content, 'utf8');
}

/**
 * 更新 tauri.conf.json 中的版本号
 * @param {string} newVersion - 新版本号
 */
function updateTauriConf(newVersion) {
    console.log(`📝 更新 ${TAURI_CONF} 版本号到 ${newVersion}`);
    
    let content = fs.readFileSync(TAURI_CONF, 'utf8');
    content = content.replace(/"version": ".*"/, `"version": "${newVersion}"`);
    fs.writeFileSync(TAURI_CONF, content, 'utf8');
}

/**
 * 更新 installer.nsi 中的版本号
 * @param {string} newVersion - 新版本号
 */
function updateInstallerNsi(newVersion) {
    console.log(`📝 更新 ${INSTALLER_NSI} 版本号到 ${newVersion}`);
    
    let content = fs.readFileSync(INSTALLER_NSI, 'utf8');
    content = content.replace(/!define APP_VERSION ".*"/, `!define APP_VERSION "${newVersion}"`);
    fs.writeFileSync(INSTALLER_NSI, content, 'utf8');
}

/**
 * 检查必要文件是否存在
 */
function checkFiles() {
    const files = [CARGO_TOML, TAURI_CONF, INSTALLER_NSI];
    
    for (const file of files) {
        if (!fs.existsSync(file)) {
            throw new Error(`❌ 错误: 文件 ${file} 不存在`);
        }
    }
}

/**
 * 执行构建命令
 */
function runBuild() {
    console.log('🔨 开始执行构建命令...');
    console.log('命令: cargo tauri build --target x86_64-pc-windows-gnu --verbose | grep -i "nsis"');
    console.log('');
    
    try {
        // 执行构建命令
        const result = execSync('cargo tauri build --target x86_64-pc-windows-gnu --verbose', {
            encoding: 'utf8',
            stdio: 'pipe'
        });
        
        // 过滤包含 "nsis" 的行
        const nsisLines = result.split('\n').filter(line => 
            line.toLowerCase().includes('nsis')
        );
        
        if (nsisLines.length > 0) {
            console.log('NSIS 相关输出:');
            nsisLines.forEach(line => console.log(line));
        } else {
            console.log('未找到包含 "nsis" 的输出行');
        }
        
    } catch (error) {
        console.error('❌ 构建过程中出现错误:');
        console.error(error.message);
        
        // 如果有输出，也显示出来
        if (error.stdout) {
            const nsisLines = error.stdout.split('\n').filter(line => 
                line.toLowerCase().includes('nsis')
            );
            if (nsisLines.length > 0) {
                console.log('\nNSIS 相关输出:');
                nsisLines.forEach(line => console.log(line));
            }
        }
        
        throw error;
    }
}

/**
 * 主函数
 */
function main() {
    try {
        console.log('🚀 开始自动版本号递增构建...');
        
        // 检查文件
        checkFiles();
        
        // 获取当前版本号
        const currentVersion = getCurrentVersion();
        console.log(`📋 当前版本号: ${currentVersion}`);
        
        // 计算新版本号
        const newVersion = incrementVersion(currentVersion);
        console.log(`🆕 新版本号: ${newVersion}`);
        
        // 更新所有文件中的版本号
        updateCargoToml(newVersion);
        updateTauriConf(newVersion);
        updateInstallerNsi(newVersion);
        
        console.log('✅ 版本号更新完成!');
        console.log('');
        
        // 执行构建
        runBuild();
        
        console.log('');
        console.log(`🎉 构建完成! 新版本: ${newVersion}`);
        
    } catch (error) {
        console.error('❌ 脚本执行失败:', error.message);
        process.exit(1);
    }
}

// 运行主函数
main();
