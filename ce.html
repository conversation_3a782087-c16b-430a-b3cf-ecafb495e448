<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>哈希算法速度测试</title>
   <!-- 更新后的CDN链接 -->
   <script src="https://cdn.jsdelivr.net/npm/spark-md5@3.0.2/spark-md5.min.js"></script>
   <script src="https://cdn.jsdelivr.net/npm/sha256@0.2.0/lib/sha256.min.js"></script>
   <script src="https://cdn.jsdelivr.net/npm/xxhashjs@0.2.2/build/xxhash.min.js"></script>
   <script src="https://cdn.jsdelivr.net/npm/blake3@2.1.7/dist/browser/blake3.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            margin-bottom: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        #fileInput {
            margin-bottom: 15px;
        }
        #results {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
        }
        .result-item {
            margin-bottom: 10px;
            padding: 8px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        progress {
            width: 100%;
            height: 20px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>哈希算法速度测试</h1>
    <div class="container">
        <input type="file" id="fileInput">
        <p>选择一个大文件进行测试（建议50MB-1GB）</p>
        <button id="startTest">开始测试</button>
        <button id="cancelTest">取消测试</button>
        <progress id="progressBar" value="0" max="100" style="display: none;"></progress>
    </div>
    <div id="results"></div>

    <script>
        const algorithms = {
            'MD5': {
                hash: async (chunk) => SparkMD5.ArrayBuffer.hash(chunk),
                description: '传统加密哈希，速度快但安全性较低'
            },
            'SHA-256': {
                hash: async (chunk) => sha256(chunk),
                description: '安全哈希标准，速度中等'
            },
            'xxHash': {
                hash: async (chunk) => XXH.h32().update(new Uint8Array(chunk)).digest().toString(16),
                description: '非加密哈希，速度极快'
            },
            'BLAKE3': {
                hash: async (chunk) => blake3(new Uint8Array(chunk)).toString(),
                description: '现代加密哈希，速度最快且安全'
            }
        };

        let cancelRequested = false;
        let file = null;
        const CHUNK_SIZE = 1024 * 1024 * 10; // 10MB chunks

        document.getElementById('fileInput').addEventListener('change', (e) => {
            file = e.target.files[0];
        });

        document.getElementById('startTest').addEventListener('click', async () => {
            if (!file) {
                alert('请先选择一个文件');
                return;
            }

            cancelRequested = false;
            document.getElementById('progressBar').style.display = 'block';
            document.getElementById('results').innerHTML = '';
            
            const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
            addResult(`<strong>测试文件:</strong> ${file.name} (${fileSizeMB} MB)`);

            // 测试每种算法
            for (const [name, algo] of Object.entries(algorithms)) {
                if (cancelRequested) break;
                
                const startTime = performance.now();
                try {
                    const hash = await calculateHash(file, algo.hash, name);
                    const endTime = performance.now();
                    const duration = ((endTime - startTime) / 1000).toFixed(2);
                    const speed = (file.size / (1024 * 1024) / (endTime - startTime) * 1000).toFixed(2);
                    
                    addResult(`
                        <div class="result-item">
                            <strong>${name}:</strong> ${hash.substring(0, 20)}...<br>
                            <small>${algo.description}</small><br>
                            耗时: ${duration} 秒 | 速度: ${speed} MB/s
                        </div>
                    `);
                } catch (error) {
                    addResult(`<div class="result-item" style="color:red;">${name} 测试失败: ${error.message}</div>`);
                }
            }
            
            document.getElementById('progressBar').style.display = 'none';
        });

        document.getElementById('cancelTest').addEventListener('click', () => {
            cancelRequested = true;
        });

        async function calculateHash(file, hashFn, algorithmName) {
            return new Promise((resolve, reject) => {
                const fileReader = new FileReader();
                const spark = algorithmName === 'MD5' ? new SparkMD5.ArrayBuffer() : null;
                let hash = null;
                let offset = 0;
                let chunksProcessed = 0;
                const totalChunks = Math.ceil(file.size / CHUNK_SIZE);

                function readNextChunk() {
                    if (cancelRequested) {
                        reject(new Error('测试已取消'));
                        return;
                    }

                    const chunk = file.slice(offset, offset + CHUNK_SIZE);
                    fileReader.readAsArrayBuffer(chunk);
                }

                fileReader.onload = async (e) => {
                    try {
                        const chunk = e.target.result;
                        
                        if (algorithmName === 'MD5') {
                            spark.append(chunk);
                        } else {
                            const chunkHash = await hashFn(chunk);
                            if (!hash) hash = chunkHash;
                        }

                        chunksProcessed++;
                        offset += CHUNK_SIZE;
                        
                        // 更新进度条
                        const progress = (chunksProcessed / totalChunks) * 100;
                        document.getElementById('progressBar').value = progress;
                        
                        if (offset < file.size) {
                            readNextChunk();
                        } else {
                            if (algorithmName === 'MD5') {
                                resolve(spark.end());
                            } else {
                                resolve(hash);
                            }
                        }
                    } catch (error) {
                        reject(error);
                    }
                };

                fileReader.onerror = () => {
                    reject(new Error('文件读取失败'));
                };

                readNextChunk();
            });
        }

        function addResult(html) {
            document.getElementById('results').innerHTML += html;
        }
    </script>
</body>
</html>