{"$schema": "https://schema.tauri.app/config/2", "productName": "问答精灵", "version": "1.0.8", "identifier": "1", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "问答精灵", "width": 500, "height": 900, "resizable": false, "maximizable": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "nsis", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}