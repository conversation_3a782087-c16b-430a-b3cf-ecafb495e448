/**
 * 测试题目数据验证功能
 */

// 模拟验证函数
function validateQuestionData(questionData, row, questionType) {
  const errors = [];

  // 验证题目问题
  if (
    !questionData.content ||
    (typeof questionData.content === "string" &&
      questionData.content.trim() === "")
  ) {
    errors.push("题目问题不能为空");
  }

  // 验证题目难度
  if (
    !questionData.difficulty ||
    (typeof questionData.difficulty === "string" &&
      questionData.difficulty.trim() === "")
  ) {
    errors.push("题目难度不能为空");
  }

  // 验证题目类型
  if (
    !questionData.questionType ||
    (typeof questionData.questionType === "string" &&
      questionData.questionType.trim() === "")
  ) {
    errors.push("题目类型不能为空");
  }

  // 验证正确答案
  if (
    !questionData.correctAnswer ||
    (typeof questionData.correctAnswer === "string" &&
      questionData.correctAnswer.trim() === "")
  ) {
    errors.push("正确答案不能为空");
  }

  // 根据题目类型进行特定验证
  if (questionType === "选择题") {
    // 选择题验证选项
    const optionsText = row["选项"];
    if (!optionsText || optionsText.trim() === "") {
      errors.push("选择题的选项不能为空");
    } else {
      // 检查选项是否有内容
      const lines = optionsText.split("\n").filter((line) => line.trim());
      const validOptions = lines.filter((line) => {
        const trimmedLine = line.trim();
        if (trimmedLine) {
          const cleanOption = trimmedLine.replace(/^[A-Z][:：]\s*/, "");
          const finalOption = cleanOption.replace(/【答案】/g, "").trim();
          return finalOption !== "";
        }
        return false;
      });

      if (validOptions.length < 2) {
        errors.push("选择题至少需要2个有效选项");
      }
    }
  } else if (questionType === "问答题") {
    // 问答题特殊验证
    const optionsText = row["选项"];
    
    // 1. 检查是否存在选项列且有值
    if (optionsText && optionsText.trim() !== "") {
      errors.push("问答题不应该有选项内容，请清空选项列");
    }
    
    // 2. 检查答案不能是"是"或"否"
    const answer = questionData.correctAnswer;
    if (answer && typeof answer === "string") {
      const trimmedAnswer = answer.trim();
      if (trimmedAnswer === "是" || trimmedAnswer === "否") {
        errors.push('问答题的答案不能是"是"或"否"，请提供详细答案');
      }
    }
  } else if (questionType === "判断题") {
    // 判断题严格验证答案
    const answer = questionData.correctAnswer;
    if (answer && typeof answer === "string") {
      const trimmedAnswer = answer.trim();
      if (trimmedAnswer !== "是" && trimmedAnswer !== "否") {
        errors.push(`判断题的答案必须是"是"或"否"，当前答案："${trimmedAnswer}"`);
      }
    } else {
      errors.push('判断题的答案必须是"是"或"否"');
    }
  }

  return errors;
}

// 测试用例
const testCases = [
  // 问答题测试
  {
    name: "问答题 - 正常情况",
    questionData: {
      content: "什么是JavaScript？",
      difficulty: "简单",
      questionType: "问答题",
      correctAnswer: "JavaScript是一种编程语言"
    },
    row: { "选项": "" },
    questionType: "问答题",
    expectedErrors: 0
  },
  {
    name: "问答题 - 有选项内容（应报错）",
    questionData: {
      content: "什么是JavaScript？",
      difficulty: "简单", 
      questionType: "问答题",
      correctAnswer: "JavaScript是一种编程语言"
    },
    row: { "选项": "A: 选项1\nB: 选项2" },
    questionType: "问答题",
    expectedErrors: 1
  },
  {
    name: "问答题 - 答案是'是'（应报错）",
    questionData: {
      content: "JavaScript是编程语言吗？",
      difficulty: "简单",
      questionType: "问答题", 
      correctAnswer: "是"
    },
    row: { "选项": "" },
    questionType: "问答题",
    expectedErrors: 1
  },
  {
    name: "问答题 - 答案是'否'（应报错）",
    questionData: {
      content: "JavaScript是数据库吗？",
      difficulty: "简单",
      questionType: "问答题",
      correctAnswer: "否"
    },
    row: { "选项": "" },
    questionType: "问答题",
    expectedErrors: 1
  },
  
  // 判断题测试
  {
    name: "判断题 - 答案是'是'（正常）",
    questionData: {
      content: "JavaScript是编程语言",
      difficulty: "简单",
      questionType: "判断题",
      correctAnswer: "是"
    },
    row: { "选项": "" },
    questionType: "判断题",
    expectedErrors: 0
  },
  {
    name: "判断题 - 答案是'否'（正常）",
    questionData: {
      content: "JavaScript是数据库",
      difficulty: "简单",
      questionType: "判断题",
      correctAnswer: "否"
    },
    row: { "选项": "" },
    questionType: "判断题",
    expectedErrors: 0
  },
  {
    name: "判断题 - 答案是'对'（应报错）",
    questionData: {
      content: "JavaScript是编程语言",
      difficulty: "简单",
      questionType: "判断题",
      correctAnswer: "对"
    },
    row: { "选项": "" },
    questionType: "判断题",
    expectedErrors: 1
  },
  {
    name: "判断题 - 答案是'错'（应报错）",
    questionData: {
      content: "JavaScript是数据库",
      difficulty: "简单",
      questionType: "判断题",
      correctAnswer: "错"
    },
    row: { "选项": "" },
    questionType: "判断题",
    expectedErrors: 1
  },
  
  // 选择题测试
  {
    name: "选择题 - 正常情况",
    questionData: {
      content: "JavaScript的创造者是？",
      difficulty: "简单",
      questionType: "选择题",
      correctAnswer: "A"
    },
    row: { "选项": "A: Brendan Eich\nB: Bill Gates\nC: Steve Jobs" },
    questionType: "选择题",
    expectedErrors: 0
  }
];

console.log('🧪 开始测试题目数据验证功能...\n');

let passedTests = 0;
let totalTests = testCases.length;

testCases.forEach((testCase, index) => {
  const errors = validateQuestionData(testCase.questionData, testCase.row, testCase.questionType);
  const actualErrors = errors.length;
  
  if (actualErrors === testCase.expectedErrors) {
    console.log(`✅ 测试 ${index + 1}: ${testCase.name} - 通过`);
    passedTests++;
  } else {
    console.log(`❌ 测试 ${index + 1}: ${testCase.name} - 失败`);
    console.log(`   预期错误数: ${testCase.expectedErrors}, 实际错误数: ${actualErrors}`);
    if (errors.length > 0) {
      console.log(`   错误信息: ${errors.join('; ')}`);
    }
  }
});

console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`);

if (passedTests === totalTests) {
  console.log('🎉 所有测试通过！题目验证功能正常工作。');
} else {
  console.log('⚠️  部分测试失败，请检查验证逻辑。');
}
