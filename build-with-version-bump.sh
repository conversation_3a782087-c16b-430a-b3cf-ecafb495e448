#!/bin/bash

# 自动版本号递增构建脚本
# 使用方法: ./build-with-version-bump.sh

set -e  # 遇到错误立即退出

echo "🚀 开始自动版本号递增构建..."

# 定义文件路径
CARGO_TOML="src-tauri/Cargo.toml"
TAURI_CONF="src-tauri/tauri.conf.json"
INSTALLER_NSI="src-tauri/nsis/installer.nsi"

# 函数：从版本号字符串中提取并递增版本号
increment_version() {
    local version=$1
    local major minor patch
    
    # 解析版本号 (例如: 1.0.4 -> major=1, minor=0, patch=4)
    IFS='.' read -r major minor patch <<< "$version"
    
    # 递增补丁版本号
    patch=$((patch + 1))
    
    # 返回新版本号
    echo "${major}.${minor}.${patch}"
}

# 函数：获取当前版本号
get_current_version() {
    grep -E '^version = ' "$CARGO_TOML" | sed 's/version = "\(.*\)"/\1/'
}

# 函数：更新 Cargo.toml 中的版本号
update_cargo_toml() {
    local new_version=$1
    echo "📝 更新 $CARGO_TOML 版本号到 $new_version"
    
    # 使用 sed 替换版本号
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/^version = \".*\"/version = \"$new_version\"/" "$CARGO_TOML"
    else
        # Linux
        sed -i "s/^version = \".*\"/version = \"$new_version\"/" "$CARGO_TOML"
    fi
}

# 函数：更新 tauri.conf.json 中的版本号
update_tauri_conf() {
    local new_version=$1
    echo "📝 更新 $TAURI_CONF 版本号到 $new_version"
    
    # 使用 sed 替换版本号
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/\"version\": \".*\"/\"version\": \"$new_version\"/" "$TAURI_CONF"
    else
        # Linux
        sed -i "s/\"version\": \".*\"/\"version\": \"$new_version\"/" "$TAURI_CONF"
    fi
}

# 函数：更新 installer.nsi 中的版本号
update_installer_nsi() {
    local new_version=$1
    echo "📝 更新 $INSTALLER_NSI 版本号到 $new_version"
    
    # 使用 sed 替换版本号
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/!define APP_VERSION \".*\"/!define APP_VERSION \"$new_version\"/" "$INSTALLER_NSI"
    else
        # Linux
        sed -i "s/!define APP_VERSION \".*\"/!define APP_VERSION \"$new_version\"/" "$INSTALLER_NSI"
    fi
}

# 主逻辑
main() {
    # 检查必要文件是否存在
    for file in "$CARGO_TOML" "$TAURI_CONF" "$INSTALLER_NSI"; do
        if [[ ! -f "$file" ]]; then
            echo "❌ 错误: 文件 $file 不存在"
            exit 1
        fi
    done
    
    # 获取当前版本号
    current_version=$(get_current_version)
    echo "📋 当前版本号: $current_version"
    
    # 计算新版本号
    new_version=$(increment_version "$current_version")
    echo "🆕 新版本号: $new_version"
    
    # 更新所有文件中的版本号
    update_cargo_toml "$new_version"
    update_tauri_conf "$new_version"
    update_installer_nsi "$new_version"
    
    echo "✅ 版本号更新完成!"
    echo ""
    
    # 执行构建命令
    echo "🔨 开始执行构建命令..."
    echo "命令: cargo tauri build --target x86_64-pc-windows-gnu --verbose | grep -i \"nsis\""
    echo ""
    
    # 执行构建
    cargo tauri build --target x86_64-pc-windows-gnu --verbose | grep -i "nsis"
    
    echo ""
    echo "🎉 构建完成! 新版本: $new_version"
}

# 运行主函数
main "$@"
