<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音量控制示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .container {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .volume-display {
            font-size: 24px;
            margin: 20px 0;
        }
        .volume-bar {
            width: 200px;
            height: 20px;
            background: #ddd;
            margin: 0 auto;
            border-radius: 10px;
            overflow: hidden;
        }
        .volume-level {
            height: 100%;
            background: #4CAF50;
            width: 50%;
        }
        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .instructions {
            margin-top: 30px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container" tabindex="0" id="volumeControl">
        <h1>音量控制</h1>
        <div class="volume-display">
            当前音量: <span id="currentVolume">5</span>/10
        </div>
        <div class="volume-bar">
            <div class="volume-level" id="volumeBar"></div>
        </div>
        <div class="instructions">
            使用 <strong>Alt + =</strong> 增加音量<br>
            使用 <strong>Alt + -</strong> 减少音量
        </div>
        <div class="notification" id="notification"></div>
    </div>

    <script>
        // 获取DOM元素
        const volumeControl = document.getElementById('volumeControl');
        const currentVolume = document.getElementById('currentVolume');
        const volumeBar = document.getElementById('volumeBar');
        const notification = document.getElementById('notification');
        
        // 初始音量
        let volume = 5;
        
        // 更新音量显示
        function updateVolumeDisplay() {
            currentVolume.textContent = volume;
            volumeBar.style.width = `${volume * 10}%`;
        }
        
        // 显示通知
        function showNotification(message) {
            notification.textContent = message;
            notification.style.opacity = '1';
            setTimeout(() => {
                notification.style.opacity = '0';
            }, 1000);
        }
        
        // 键盘事件处理函数
        function handleKeyDown(event) {
            // 检查是否按下了 Alt 键
            if (event.altKey) {
                let newVolume = volume;
                console.log(event.altKey,111111);
                
                if (event.key === "=" || event.key === "+" || event.key === "≠") {
                    // Alt + = 或 Alt + + 增加音量
                    event.preventDefault();
                    newVolume = Math.min(10, volume + 1);
                    showNotification(`音量增加到 ${newVolume}`);
                } else if (event.key === "-" || event.key === "–") {
                    // Alt + - 减少音量
                    event.preventDefault();
                    newVolume = Math.max(0, volume - 1);
                    showNotification(`音量减少到 ${newVolume}`);
                }
                
                // 如果音量有变化
                if (newVolume !== volume) {
                    volume = newVolume;
                    updateVolumeDisplay();
                    // 这里可以添加实际的音量控制逻辑
                    // 例如: audio.volume = volume / 10;
                }
            }
        }
        
        // 添加事件监听器
        volumeControl.addEventListener('keydown', handleKeyDown);
        
        // 初始化显示
        updateVolumeDisplay();
        
        // 让div可聚焦
        volumeControl.focus();
    </script>
</body>
</html>