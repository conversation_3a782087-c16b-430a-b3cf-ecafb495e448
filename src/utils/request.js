import axios from 'axios';
import { ElMessage } from 'element-plus';
import {generateAuthHeaders} from "./utils"
// 创建axios实例
const service = axios.create({
  baseURL: 'http://api2.kkzhw.com/mall-portal',
  timeout: 60000
});

// 请求拦截器
service.interceptors.request.use(
  async config => {
    // 在这里添加header，比如token等
    const headers = await generateAuthHeaders();
    config.headers = {
      ...config.headers,
      ...headers
    };
    return config;
  },
  error => {
    console.log(error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
    if (res.code === 200) {
      return res;
    } else {
      
      if(res.code==500 && res.message=='设备未注册'){
        localStorage.removeItem('skdToken1');
        // 使用vue3的router跳转
        import('@/router').then(module => {
          const router = module.default || module.router;
          if (router) {
            router.push('/');
          } else {
            window.location.href = '/';
          }
        }).catch(() => {
          window.location.href = '/';
        });
      }
      return res
    }
  },
  error => {
    console.log('err' + error);
    // ElMessage.error(error.message || '网络错误');
    return Promise.reject(error);
  }
);

export default service;