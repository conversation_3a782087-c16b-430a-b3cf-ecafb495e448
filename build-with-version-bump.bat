@echo off
setlocal enabledelayedexpansion

REM 自动版本号递增构建脚本 (Windows版本)
REM 使用方法: build-with-version-bump.bat

echo 🚀 开始自动版本号递增构建...

REM 定义文件路径
set "CARGO_TOML=src-tauri\Cargo.toml"
set "TAURI_CONF=src-tauri\tauri.conf.json"
set "INSTALLER_NSI=src-tauri\nsis\installer.nsi"

REM 检查必要文件是否存在
if not exist "%CARGO_TOML%" (
    echo ❌ 错误: 文件 %CARGO_TOML% 不存在
    exit /b 1
)
if not exist "%TAURI_CONF%" (
    echo ❌ 错误: 文件 %TAURI_CONF% 不存在
    exit /b 1
)
if not exist "%INSTALLER_NSI%" (
    echo ❌ 错误: 文件 %INSTALLER_NSI% 不存在
    exit /b 1
)

REM 获取当前版本号
for /f "tokens=3 delims= " %%a in ('findstr "^version = " "%CARGO_TOML%"') do (
    set "current_version=%%a"
    set "current_version=!current_version:"=!"
)

echo 📋 当前版本号: !current_version!

REM 解析版本号并递增
for /f "tokens=1,2,3 delims=." %%a in ("!current_version!") do (
    set "major=%%a"
    set "minor=%%b"
    set /a "patch=%%c+1"
)

set "new_version=!major!.!minor!.!patch!"
echo 🆕 新版本号: !new_version!

REM 更新 Cargo.toml
echo 📝 更新 %CARGO_TOML% 版本号到 !new_version!
powershell -Command "(Get-Content '%CARGO_TOML%') -replace '^version = \".*\"', 'version = \"!new_version!\"' | Set-Content '%CARGO_TOML%'"

REM 更新 tauri.conf.json
echo 📝 更新 %TAURI_CONF% 版本号到 !new_version!
powershell -Command "(Get-Content '%TAURI_CONF%') -replace '\"version\": \".*\"', '\"version\": \"!new_version!\"' | Set-Content '%TAURI_CONF%'"

REM 更新 installer.nsi
echo 📝 更新 %INSTALLER_NSI% 版本号到 !new_version!
powershell -Command "(Get-Content '%INSTALLER_NSI%') -replace '!define APP_VERSION \".*\"', '!define APP_VERSION \"!new_version!\"' | Set-Content '%INSTALLER_NSI%'"

echo ✅ 版本号更新完成!
echo.

REM 执行构建命令
echo 🔨 开始执行构建命令...
echo 命令: cargo tauri build --target x86_64-pc-windows-gnu --verbose ^| findstr /i "nsis"
echo.

REM 执行构建
cargo tauri build --target x86_64-pc-windows-gnu --verbose | findstr /i "nsis"

echo.
echo 🎉 构建完成! 新版本: !new_version!

pause
